package server

import (
	healthDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/health"
	userDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/user"
	userRoleDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/user_role"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/config"
	healthusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/health"
	userusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/user"
)

var (
	// usecases
	HealthUseCase *healthusecase.HealthUseCase
	UserUseCase   *userusecase.UserUseCase

	// domains
	healthDomain   healthDmn.HealthDomain
	userDomain     userDmn.UserDomain
	userRoleDomain userRoleDmn.UserRoleDomain
)

func Init(mode ...string) error {
	config.InitDatabase()
	config.InitGoAdmin()
	InitAccountingSystem()

	return nil
}

func InitAccountingSystem() {
	healthDomain = healthDmn.InitHealthDomain(healthDmn.HealthResource{})
	userDomain = userDmn.InitUserDomain(userDmn.UserResource{})
	userRoleDomain = userRoleDmn.InitUserRoleDomain(userRoleDmn.UserRoleResource{})

	healthDomains := healthusecase.Domains{
		HealthDomain: &healthDomain,
	}

	userDomains := userusecase.Domains{
		UserDomain:     &userDomain,
		UserRoleDomain: &userRoleDomain,
	}

	HealthUseCase = healthusecase.InitHealthUseCase(healthDomains)
	UserUseCase = userusecase.InitUserUseCase(userDomains)
}
