package site_report

import (
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/usecase/site_report"
)

func validateGetList(req sitereport.GetListReq) error {
	if req.StartDate == "" {
		return ErrStartDateRequired
	}

	if req.EndDate == "" {
		return ErrEndDateRequired
	}

	// Validate date format
	_, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return ErrInvalidDateFormat
	}

	_, err = time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		return ErrInvalidDateFormat
	}

	return nil
}
