package sitereport

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	SiteReportDomainItf interface {
		GetList(ctx context.Context, param GetListParam) ([]SiteReport, error)
	}

	SiteReportResourceItf interface {
		getList(ctx context.Context, param GetListParam) ([]SiteReport, error)
	}
)

// GetList retrieves site reports filtered by date range.
func (d *SiteReportDomain) GetList(ctx context.Context, param GetListParam) ([]SiteReport, error) {
	reports, err := d.resource.getList(ctx, param)
	if err != nil {
		return []SiteReport{}, log.LogError(err, nil)
	}
	return reports, nil
}
