package sitereport

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// getList fetches site reports filtered by date range.
func (rsc SiteReportResource) getList(ctx context.Context, param GetListParam) ([]SiteReport, error) {
	var reports []SiteReport

	db := dbmanager.Manager().WithContext(ctx)
	
	// Filter by work_date range and exclude soft deleted records
	err := db.Where("work_date >= ? AND work_date <= ?", param.StartDate, param.EndDate).
		Where("deleted_at IS NULL").
		Order("work_date ASC, id ASC").
		Find(&reports).Error
	
	if err != nil {
		return []SiteReport{}, log.LogError(err, nil)
	}

	return reports, nil
}
