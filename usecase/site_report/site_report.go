package sitereport

import (
	"context"
	"fmt"
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	sitereportDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report"
)

func (uc *SiteReportUseCase) GetList(ctx context.Context, req GetListReq) ([]GetListResp, error) {
	// Parse dates
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return []GetListResp{}, log.LogError(ErrInvalidDateFormat, nil)
	}

	endDate, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		return []GetListResp{}, log.LogError(ErrInvalidDateFormat, nil)
	}

	// Get site reports from domain
	param := sitereportDmn.GetListParam{
		StartDate: startDate,
		EndDate:   endDate,
	}

	reports, err := uc.sitereport.GetList(ctx, param)
	if err != nil {
		return []GetListResp{}, log.LogError(err, nil)
	}

	// Aggregate data hierarchically
	return aggregateReports(reports), nil
}

// aggregateReports organizes site reports into hierarchical structure by year -> month -> date
func aggregateReports(reports []sitereportDmn.SiteReport) []GetListResp {
	// Group by year -> month -> date
	yearMap := make(map[int]map[int]map[int][]sitereportDmn.SiteReport)

	for _, report := range reports {
		year := report.WorkDate.Year()
		month := int(report.WorkDate.Month())
		day := report.WorkDate.Day()

		if yearMap[year] == nil {
			yearMap[year] = make(map[int]map[int][]sitereportDmn.SiteReport)
		}
		if yearMap[year][month] == nil {
			yearMap[year][month] = make(map[int][]sitereportDmn.SiteReport)
		}
		if yearMap[year][month][day] == nil {
			yearMap[year][month][day] = []sitereportDmn.SiteReport{}
		}

		yearMap[year][month][day] = append(yearMap[year][month][day], report)
	}

	// Convert to response structure
	var result []GetListResp

	for year, monthMap := range yearMap {
		yearResp := GetListResp{
			Year:  fmt.Sprintf("%d年", year),
			Month: []MonthData{},
		}

		for month, dayMap := range monthMap {
			monthData := MonthData{
				Value:       fmt.Sprintf("%02d月", month),
				Worker:      0,
				TotalAmount: 0,
				Date:        []DateData{},
			}

			for day, dayReports := range dayMap {
				dateData := DateData{
					Value:       fmt.Sprintf("%d日", day),
					Worker:      0,
					TotalAmount: 0,
					Report:      []ReportData{},
				}

				// Process reports for this date
				for _, report := range dayReports {
					reportData := ReportData{
						SiteReportID: report.ID,
						SiteName:     report.SiteName,
						Worker:       report.Worker,
						TotalAmount:  report.TotalAmount,
						BStartTime:   report.BStartTime.Format("15:04"),
						BEndTime:     report.BEndTime.Format("15:04"),
						Note:         report.Note,
						IsLocked:     report.IsLocked,
					}

					dateData.Report = append(dateData.Report, reportData)
					dateData.Worker += report.Worker
					dateData.TotalAmount += report.TotalAmount
				}

				monthData.Date = append(monthData.Date, dateData)
				monthData.Worker += dateData.Worker
				monthData.TotalAmount += dateData.TotalAmount
			}

			yearResp.Month = append(yearResp.Month, monthData)
		}

		result = append(result, yearResp)
	}

	return result
}
